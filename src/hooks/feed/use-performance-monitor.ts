import { useRef, useEffect, useState, useCallback } from 'react'

interface PerformanceMetrics {
  fps: number
  memoryUsage: number
  loadedItems: number
  averageLoadTime: number
  scrollPerformance: {
    smoothness: number
    jankCount: number
    averageFrameTime: number
  }
  networkMetrics: {
    totalRequests: number
    failedRequests: number
    averageResponseTime: number
  }
}

interface UsePerformanceMonitorOptions {
  enabled?: boolean
  sampleInterval?: number // How often to sample FPS (ms)
  memoryCheckInterval?: number // How often to check memory (ms)
  onMetricsUpdate?: (metrics: PerformanceMetrics) => void
  onPerformanceIssue?: (issue: string, severity: 'low' | 'medium' | 'high') => void
}

export function usePerformanceMonitor(options: UsePerformanceMonitorOptions = {}) {
  const {
    enabled = process.env.NODE_ENV === 'development',
    sampleInterval = 1000,
    memoryCheckInterval = 5000,
    onMetricsUpdate,
    onPerformanceIssue,
  } = options

  const [metrics, setMetrics] = useState<PerformanceMetrics>({
    fps: 60,
    memoryUsage: 0,
    loadedItems: 0,
    averageLoadTime: 0,
    scrollPerformance: {
      smoothness: 100,
      jankCount: 0,
      averageFrameTime: 16.67, // 60fps = 16.67ms per frame
    },
    networkMetrics: {
      totalRequests: 0,
      failedRequests: 0,
      averageResponseTime: 0,
    },
  })

  // FPS monitoring
  const fpsRef = useRef({
    frames: 0,
    lastTime: performance.now(),
    frameId: 0,
  })

  const frameTimes = useRef<number[]>([])
  const loadTimes = useRef<number[]>([])
  const networkRequests = useRef<Array<{ startTime: number; endTime?: number; failed?: boolean }>>([])

  // FPS calculation
  const calculateFPS = useCallback(() => {
    if (!enabled) return

    const now = performance.now()
    fpsRef.current.frames++

    // Calculate frame time
    const frameTime = now - fpsRef.current.lastTime
    frameTimes.current.push(frameTime)
    
    // Keep only last 60 frame times (1 second at 60fps)
    if (frameTimes.current.length > 60) {
      frameTimes.current.shift()
    }

    fpsRef.current.lastTime = now
    fpsRef.current.frameId = requestAnimationFrame(calculateFPS)
  }, [enabled])

  // Start FPS monitoring
  useEffect(() => {
    if (!enabled) return

    fpsRef.current.frameId = requestAnimationFrame(calculateFPS)

    return () => {
      if (fpsRef.current.frameId) {
        cancelAnimationFrame(fpsRef.current.frameId)
      }
    }
  }, [enabled, calculateFPS])

  // Memory monitoring
  const checkMemoryUsage = useCallback(() => {
    if (!enabled || typeof window === 'undefined') return 0

    // Use Performance API if available
    if ('memory' in performance) {
      const memory = (performance as any).memory
      return Math.round(memory.usedJSHeapSize / 1024 / 1024) // Convert to MB
    }

    return 0
  }, [enabled])

  // Network request tracking
  const trackNetworkRequest = useCallback((url: string) => {
    if (!enabled) return () => {}

    const startTime = performance.now()
    const requestId = networkRequests.current.length

    networkRequests.current.push({ startTime })

    return {
      success: () => {
        const endTime = performance.now()
        if (networkRequests.current[requestId]) {
          networkRequests.current[requestId].endTime = endTime
        }
      },
      error: () => {
        const endTime = performance.now()
        if (networkRequests.current[requestId]) {
          networkRequests.current[requestId].endTime = endTime
          networkRequests.current[requestId].failed = true
        }
      },
    }
  }, [enabled])

  // Content load time tracking
  const trackContentLoad = useCallback((startTime: number) => {
    if (!enabled) return

    const loadTime = performance.now() - startTime
    loadTimes.current.push(loadTime)

    // Keep only last 50 load times
    if (loadTimes.current.length > 50) {
      loadTimes.current.shift()
    }
  }, [enabled])

  // Calculate scroll performance metrics
  const calculateScrollPerformance = useCallback(() => {
    if (frameTimes.current.length === 0) {
      return {
        smoothness: 100,
        jankCount: 0,
        averageFrameTime: 16.67,
      }
    }

    const avgFrameTime = frameTimes.current.reduce((sum, time) => sum + time, 0) / frameTimes.current.length
    const jankThreshold = 16.67 * 1.5 // 1.5x the ideal frame time
    const jankCount = frameTimes.current.filter(time => time > jankThreshold).length
    const smoothness = Math.max(0, 100 - (jankCount / frameTimes.current.length) * 100)

    return {
      smoothness: Math.round(smoothness),
      jankCount,
      averageFrameTime: Math.round(avgFrameTime * 100) / 100,
    }
  }, [])

  // Calculate network metrics
  const calculateNetworkMetrics = useCallback(() => {
    const completedRequests = networkRequests.current.filter(req => req.endTime)
    const failedRequests = completedRequests.filter(req => req.failed)
    
    const totalResponseTime = completedRequests.reduce((sum, req) => {
      return sum + (req.endTime! - req.startTime)
    }, 0)

    return {
      totalRequests: networkRequests.current.length,
      failedRequests: failedRequests.length,
      averageResponseTime: completedRequests.length > 0 
        ? Math.round(totalResponseTime / completedRequests.length)
        : 0,
    }
  }, [])

  // Update metrics periodically
  useEffect(() => {
    if (!enabled) return

    const interval = setInterval(() => {
      const now = performance.now()
      const timeDiff = now - fpsRef.current.lastTime
      const currentFPS = timeDiff > 0 ? Math.round(fpsRef.current.frames / (timeDiff / 1000)) : 60

      // Reset frame counter
      fpsRef.current.frames = 0
      fpsRef.current.lastTime = now

      const newMetrics: PerformanceMetrics = {
        fps: currentFPS,
        memoryUsage: checkMemoryUsage(),
        loadedItems: 0, // This should be set externally
        averageLoadTime: loadTimes.current.length > 0 
          ? Math.round(loadTimes.current.reduce((sum, time) => sum + time, 0) / loadTimes.current.length)
          : 0,
        scrollPerformance: calculateScrollPerformance(),
        networkMetrics: calculateNetworkMetrics(),
      }

      setMetrics(newMetrics)
      onMetricsUpdate?.(newMetrics)

      // Check for performance issues
      if (currentFPS < 30) {
        onPerformanceIssue?.('Low FPS detected', 'high')
      } else if (currentFPS < 45) {
        onPerformanceIssue?.('Moderate FPS drop detected', 'medium')
      }

      if (newMetrics.memoryUsage > 100) {
        onPerformanceIssue?.('High memory usage detected', 'high')
      } else if (newMetrics.memoryUsage > 50) {
        onPerformanceIssue?.('Moderate memory usage detected', 'medium')
      }

      if (newMetrics.scrollPerformance.smoothness < 70) {
        onPerformanceIssue?.('Poor scroll performance detected', 'medium')
      }
    }, sampleInterval)

    return () => clearInterval(interval)
  }, [enabled, sampleInterval, checkMemoryUsage, calculateScrollPerformance, calculateNetworkMetrics, onMetricsUpdate, onPerformanceIssue])

  // Memory check interval
  useEffect(() => {
    if (!enabled) return

    const interval = setInterval(() => {
      const memoryUsage = checkMemoryUsage()
      setMetrics(prev => ({ ...prev, memoryUsage }))
    }, memoryCheckInterval)

    return () => clearInterval(interval)
  }, [enabled, memoryCheckInterval, checkMemoryUsage])

  // Performance mark helpers
  const markStart = useCallback((name: string) => {
    if (!enabled || typeof performance === 'undefined') return

    performance.mark(`${name}-start`)
  }, [enabled])

  const markEnd = useCallback((name: string) => {
    if (!enabled || typeof performance === 'undefined') return

    performance.mark(`${name}-end`)
    try {
      performance.measure(name, `${name}-start`, `${name}-end`)
    } catch (error) {
      console.warn('Performance measurement failed:', error)
    }
  }, [enabled])

  // Get performance entries
  const getPerformanceEntries = useCallback((name?: string) => {
    if (!enabled || typeof performance === 'undefined') return []

    if (name) {
      return performance.getEntriesByName(name)
    }
    return performance.getEntries()
  }, [enabled])

  // Clear performance data
  const clearPerformanceData = useCallback(() => {
    frameTimes.current = []
    loadTimes.current = []
    networkRequests.current = []
    
    if (typeof performance !== 'undefined') {
      performance.clearMarks()
      performance.clearMeasures()
    }
  }, [])

  return {
    metrics,
    trackNetworkRequest,
    trackContentLoad,
    markStart,
    markEnd,
    getPerformanceEntries,
    clearPerformanceData,
    isEnabled: enabled,
  }
}
