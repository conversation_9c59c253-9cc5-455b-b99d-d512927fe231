"use client"

import React, { useEffect, use<PERSON>allback, useRef, useState } from 'react'
import { cn } from '@/lib/utils'
import { VerticalFeedContainerProps, InteractionData } from '@/types/feed'
import { useFeedData } from '@/hooks/feed/use-feed-data'
import { useContentInteractions } from '@/hooks/feed/use-content-interactions'
import { useContentPreloader } from '@/hooks/feed/use-content-preloader'
import { useMediaAutoplay } from '@/hooks/feed/use-media-autoplay'
import { usePerformanceMonitor } from '@/hooks/feed/use-performance-monitor'
import { TikTokContentCard } from './TikTokContentCard'
import { TikTokFeedNavigation } from './TikTokFeedNavigation'
import { TikTokMoodSelector } from './TikTokMoodSelector'
import { useMoodFilter } from '@/hooks/feed/use-mood-filter'
import { usePlayerStore } from '@/lib/stores/enhanced-player-store'
import { Loader2, Refresh<PERSON><PERSON>, AlertCircle } from 'lucide-react'
import { But<PERSON> } from '@/components/ui/button'
import {
  Carousel,
  CarouselContent,
  CarouselItem,
  type CarouselApi
} from '@/components/ui/carousel'

interface TikTokFeedContainerProps extends VerticalFeedContainerProps {
  showNavigation?: boolean
  showMoodSelector?: boolean
  enableHapticFeedback?: boolean
  snapThreshold?: number
  preloadDistance?: number
  offloadDistance?: number
}

export function TikTokFeedContainer({
  feedType,
  filters,
  onItemChange,
  onInteraction,
  className,
  autoPlay = true,
  showNavigation = true,
  showMoodSelector = true,
  enableHapticFeedback = true,
  snapThreshold = 0.6,
  preloadDistance = 3,
  offloadDistance = 10,
}: TikTokFeedContainerProps) {
  const [api, setApi] = useState<CarouselApi>()
  const [currentIndex, setCurrentIndex] = useState(0)
  const [isScrolling, setIsScrolling] = useState(false)

  // Feed data management
  const {
    items,
    loading,
    error,
    hasMore,
    loadMore,
    refresh,
    updateFilters,
  } = useFeedData(feedType, filters)

  // Mood filtering
  const {
    selectedMoods,
    setSelectedMoods,
    moodCounts,
    clearMoods,
  } = useMoodFilter()

  // Content interactions
  const {
    react,
    unreact,
    toggleMemory,
    share,
  } = useContentInteractions()

  // Carousel API setup for vertical scrolling
  useEffect(() => {
    if (!api) return

    const onSelect = () => {
      const selectedIndex = api.selectedScrollSnap()
      setCurrentIndex(selectedIndex)

      if (items[selectedIndex]) {
        onItemChange?.(items[selectedIndex], selectedIndex)

        // Load more content when approaching the end
        if (selectedIndex >= items.length - preloadDistance && hasMore && !loading) {
          loadMore()
        }
      }
    }

    api.on('select', onSelect)
    api.on('scroll', () => setIsScrolling(true))
    api.on('settle', () => setIsScrolling(false))

    return () => {
      api.off('select', onSelect)
      api.off('scroll', () => setIsScrolling(true))
      api.off('settle', () => setIsScrolling(false))
    }
  }, [api, items, onItemChange, hasMore, loading, loadMore, preloadDistance])

  // Content preloader with basic options
  const {
    preloadedContent,
    preloadedCount,
  } = useContentPreloader(items, currentIndex, {
    preloadDistance,
    offloadDistance,
    enableImagePreload: true,
    enableVideoPreload: true,
    enableAudioPreload: false,
  })

  // Media autoplay management
  const {
    registerMediaElement,
    unregisterMediaElement,
    observeElement,
  } = useMediaAutoplay(items, currentIndex, {
    threshold: 0.6,
    autoplayDelay: 150,
    pauseDelay: 200,
    enableVideoAutoplay: autoPlay,
    enableAudioAutoplay: false,
    muteByDefault: true,
  })

  // Performance monitoring
  const {
    metrics: performanceMetrics,
  } = usePerformanceMonitor({
    enabled: process.env.NODE_ENV === 'development',
    onPerformanceIssue: (issue, severity) => {
      console.warn(`TikTok Feed Performance (${severity}):`, issue)
    },
  })

  const { pauseAll } = usePlayerStore()

  // Pause all players when scrolling starts
  useEffect(() => {
    if (isScrolling) {
      pauseAll()
    }
  }, [isScrolling, pauseAll])

  // Handle interactions with enhanced feedback
  const handleInteraction = useCallback((interaction: InteractionData) => {
    onInteraction?.(interaction)

    // Add haptic feedback for interactions
    if (enableHapticFeedback && 'vibrate' in navigator) {
      navigator.vibrate(50)
    }

    // Convert InteractionType to ReactionType for the react function
    switch (interaction.type) {
      case 'LIKE':
        react(interaction.postId, 'LIKE' as any, interaction.mood)
        break
      case 'LOVE':
        react(interaction.postId, 'LOVE' as any, interaction.mood)
        break
      case 'FIRE':
        react(interaction.postId, 'FIRE' as any, interaction.mood)
        break
      case 'MIND_BLOWN':
        react(interaction.postId, 'MIND_BLOWN' as any, interaction.mood)
        break
      case 'VIBE':
        react(interaction.postId, 'VIBE' as any, interaction.mood)
        break
      case 'MOOD_MATCH':
        react(interaction.postId, 'MOOD_MATCH' as any, interaction.mood)
        break
    }
  }, [onInteraction, react, enableHapticFeedback])

  // Enhanced keyboard navigation with carousel
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      if (event.target !== document.body || !api) return

      switch (event.key) {
        case 'ArrowUp':
          event.preventDefault()
          api.scrollPrev()
          break
        case 'ArrowDown':
          event.preventDefault()
          api.scrollNext()
          break
        case ' ': // Spacebar
          event.preventDefault()
          // Toggle play/pause for current item
          break
        case 'r':
          event.preventDefault()
          refresh()
          break
        case 'm':
          event.preventDefault()
          clearMoods()
          break
      }
    }

    document.addEventListener('keydown', handleKeyDown)
    return () => document.removeEventListener('keydown', handleKeyDown)
  }, [api, refresh, clearMoods])

  if (error) {
    return (
      <div className="flex flex-col items-center justify-center h-screen p-4 bg-black text-white">
        <AlertCircle className="h-12 w-12 text-red-500 mb-4" />
        <p className="text-lg mb-4">Failed to load feed: {error}</p>
        <Button onClick={refresh} variant="outline" className="text-white border-white hover:bg-white hover:text-black">
          <RefreshCw className="h-4 w-4 mr-2" />
          Try Again
        </Button>
      </div>
    )
  }

  return (
    <div className={cn("relative h-screen bg-black overflow-hidden", className)}>
      {/* Feed Navigation */}
      {showNavigation && (
        <TikTokFeedNavigation
          currentFeed={feedType}
          onFeedChange={(newFeedType: any) => {
            // TODO: Implement feed type change
            console.log('Feed type change:', newFeedType)
          }}
          className="absolute top-0 left-0 right-0 z-30"
        />
      )}

      {/* Mood Selector */}
      {showMoodSelector && selectedMoods.length > 0 && (
        <TikTokMoodSelector
          selectedMoods={selectedMoods}
          onMoodChange={setSelectedMoods}
          moodCounts={moodCounts}
          className="absolute top-16 left-0 right-0 z-20"
        />
      )}

      {/* Main Feed Carousel */}
      <Carousel
        orientation="vertical"
        setApi={setApi}
        opts={{
          align: "start",
          loop: false,
          skipSnaps: false,
          dragFree: false,
        }}
        className="h-full w-full"
      >
        <CarouselContent className="h-full -mt-0">
          {items.map((item, index) => (
            <CarouselItem
              key={item.post.id}
              className="h-screen pt-0"
              ref={(el) => {
                if (el) {
                  observeElement(el)
                }
              }}
            >
              <TikTokContentCard
                item={item}
                isActive={index === currentIndex}
                autoPlay={autoPlay && index === currentIndex}
                onInteraction={handleInteraction}
                onRegisterMedia={registerMediaElement}
                onUnregisterMedia={unregisterMediaElement}
                preloadedContent={preloadedContent}
                className="w-full h-full"
              />
            </CarouselItem>
          ))}

          {/* Loading indicator */}
          {loading && (
            <CarouselItem className="h-screen pt-0">
              <div className="h-full flex items-center justify-center bg-black">
                <div className="text-center text-white">
                  <Loader2 className="h-8 w-8 animate-spin mx-auto mb-4" />
                  <p className="text-sm opacity-70">Loading amazing content...</p>
                </div>
              </div>
            </CarouselItem>
          )}

          {/* End of feed indicator */}
          {!hasMore && items.length > 0 && (
            <CarouselItem className="h-screen pt-0">
              <div className="h-full flex items-center justify-center bg-black">
                <div className="text-center text-white">
                  <p className="text-lg mb-4">🎉 You've seen it all!</p>
                  <Button
                    onClick={refresh}
                    variant="outline"
                    size="sm"
                    className="text-white border-white hover:bg-white hover:text-black"
                  >
                    <RefreshCw className="h-4 w-4 mr-2" />
                    Refresh Feed
                  </Button>
                </div>
              </div>
            </CarouselItem>
          )}
        </CarouselContent>
      </Carousel>

      {/* Scroll Progress Indicator */}
      <div className="absolute right-4 top-1/2 transform -translate-y-1/2 z-10">
        <div className="flex flex-col space-y-1">
          {items.slice(Math.max(0, currentIndex - 2), currentIndex + 3).map((_, relativeIndex) => {
            const actualIndex = Math.max(0, currentIndex - 2) + relativeIndex
            const isActive = actualIndex === currentIndex

            return (
              <button
                key={actualIndex}
                onClick={() => api?.scrollTo(actualIndex)}
                className={cn(
                  "w-1 h-8 rounded-full transition-all duration-300 transform",
                  isActive
                    ? "bg-white scale-110"
                    : "bg-white/30 hover:bg-white/50 hover:scale-105"
                )}
                aria-label={`Go to item ${actualIndex + 1}`}
              />
            )
          })}
        </div>
      </div>

      {/* Performance Debug Info (Development Only) */}
      {process.env.NODE_ENV === 'development' && (
        <div className="absolute bottom-4 left-4 z-20 bg-black/80 backdrop-blur-sm text-white text-xs p-3 rounded-lg space-y-1 border border-white/20">
          <div className="font-semibold text-hvppy-400">TikTok Feed Debug</div>
          <div>Item: {currentIndex + 1}/{items.length}</div>
          <div>Has More: {hasMore ? 'Yes' : 'No'}</div>
          <div>Loading: {loading ? 'Yes' : 'No'}</div>
          <div>Preloaded: {preloadedCount}</div>

          <div className="font-semibold text-hvppy-400 mt-2">Performance</div>
          <div>FPS: {performanceMetrics.fps}</div>
          <div>Memory: {performanceMetrics.memoryUsage}MB</div>
          <div>Smoothness: {performanceMetrics.scrollPerformance?.smoothness}%</div>
          <div>Jank: {performanceMetrics.scrollPerformance?.jankCount}</div>
          <div>Avg Load: {performanceMetrics.averageLoadTime}ms</div>

          <div className="text-xs text-white/60 mt-2">
            ↑↓ Navigate • Space Play/Pause • M Clear Moods • R Refresh
          </div>
        </div>
      )}
    </div>
  )
}
