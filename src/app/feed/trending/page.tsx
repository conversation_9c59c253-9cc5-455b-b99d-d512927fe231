"use client"

import { TikTokFeedContainer } from '@/components/feed'
import { FeedType, InteractionData, FeedItem } from '@/types/feed'

export default function TrendingFeedPage() {
  const handleItemChange = (item: FeedItem, index: number) => {
    console.log('Trending feed - Current item changed:', {
      title: item.post.title,
      index,
      viewCount: item.post.viewCount,
      likeCount: item.post.likeCount,
      contentType: item.post.contentType
    })
  }

  const handleInteraction = (interaction: InteractionData) => {
    console.log('Trending feed - User interaction:', interaction)

    // Add haptic feedback for mobile devices
    if ('vibrate' in navigator) {
      navigator.vibrate(50)
    }
  }

  return (
    <div className="h-screen bg-black overflow-hidden">
      <TikTokFeedContainer
        feedType={FeedType.TRENDING}
        onItemChange={handleItemChange}
        onInteraction={handleInteraction}
        autoPlay={true}
        showNavigation={true}
        showMoodSelector={true}
        enableHapticFeedback={true}
        preloadDistance={3}
        offloadDistance={10}
        className="w-full h-full"
      />
    </div>
  )
}
