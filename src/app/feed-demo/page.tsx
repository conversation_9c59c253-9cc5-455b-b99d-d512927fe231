"use client"

import React, { useState, useCallback } from 'react'
import { cn } from '@/lib/utils'
import {
  TikTokFeedContainer,
  TikTokFeedDemo,
  LegacyVerticalFeedContainer,
  EnhancedFeedNavigation,
  EnhancedFeedControls,
  usePlayerStore,
  useMoodFilter
} from '@/components/feed'
import { FeedType, FeedItem, InteractionData } from '@/types/feed'
import { Button } from '@/components/ui/button'
import { Card } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import {
  Play,
  Pause,
  Settings,
  TrendingUp,
  Heart,
  Users,
  Sparkles,
  Monitor,
  Smartphone,
  Info,
  ArrowLeft,
  ArrowRight
} from 'lucide-react'

export default function FeedDemoPage() {
  const [feedType, setFeedType] = useState<FeedType>(FeedType.DISCOVER)
  const [currentIndex, setCurrentIndex] = useState(0)
  const [totalItems, setTotalItems] = useState(0)
  const [showControls, setShowControls] = useState(false)
  const [viewMode, setViewMode] = useState<'mobile' | 'desktop'>('mobile')
  const [showPerformanceInfo, setShowPerformanceInfo] = useState(false)
  const [demoMode, setDemoMode] = useState<'tiktok' | 'legacy'>('tiktok')

  const { isPlaying, currentPlayer } = usePlayerStore()
  const { selectedMoods } = useMoodFilter()

  // Handle item changes in the feed
  const handleItemChange = useCallback((item: FeedItem, index: number) => {
    setCurrentIndex(index)
    console.log('📱 Current item:', item.post.title, 'Index:', index)
  }, [])

  // Handle user interactions
  const handleInteraction = useCallback((interaction: InteractionData) => {
    console.log('👆 User interaction:', interaction.type, 'Post:', interaction.postId)
  }, [])

  // Feed type handlers
  const handleFeedTypeChange = useCallback((type: FeedType) => {
    setFeedType(type)
    setCurrentIndex(0)
  }, [])

  // Navigation handlers
  const handlePrevious = useCallback(() => {
    if (currentIndex > 0) {
      setCurrentIndex(currentIndex - 1)
    }
  }, [currentIndex])

  const handleNext = useCallback(() => {
    setCurrentIndex(currentIndex + 1)
  }, [currentIndex])

  const handleTogglePlay = useCallback(() => {
    // This will be handled by the player store
    console.log('🎵 Toggle play/pause')
  }, [])

  const handleScrollToIndex = useCallback((index: number) => {
    setCurrentIndex(index)
  }, [])

  const handleShuffle = useCallback(() => {
    console.log('🔀 Shuffle feed')
  }, [])

  // Get feed filters based on selected moods
  const filters = {
    moods: selectedMoods.length > 0 ? selectedMoods : undefined,
    timeRange: 'week' as const,
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-purple-900 via-blue-900 to-indigo-900">
      {/* Header */}
      <div className="fixed top-0 left-0 right-0 z-50 bg-black/20 backdrop-blur-md border-b border-white/10">
        <div className="flex items-center justify-between p-4">
          <div className="flex items-center gap-3">
            <h1 className="text-2xl font-bold text-white">HVPPY Feed Demo</h1>
            <Badge variant="secondary" className={cn(
              "text-white",
              demoMode === 'tiktok' ? "bg-purple-500/20" : "bg-blue-500/20"
            )}>
              {demoMode === 'tiktok' ? 'TikTok-Inspired' : 'Legacy Feed'}
            </Badge>
          </div>

          <div className="flex items-center gap-2">
            {/* Demo Mode Toggle */}
            <div className="flex bg-black/30 rounded-lg p-1">
              <Button
                variant={demoMode === 'tiktok' ? 'default' : 'ghost'}
                size="sm"
                onClick={() => setDemoMode('tiktok')}
                className="text-white"
              >
                <Sparkles className="w-4 h-4 mr-1" />
                TikTok
              </Button>
              <Button
                variant={demoMode === 'legacy' ? 'default' : 'ghost'}
                size="sm"
                onClick={() => setDemoMode('legacy')}
                className="text-white"
              >
                <Monitor className="w-4 h-4 mr-1" />
                Legacy
              </Button>
            </div>

            {/* View Mode Toggle (only for legacy) */}
            {demoMode === 'legacy' && (
              <div className="flex bg-black/30 rounded-lg p-1">
                <Button
                  variant={viewMode === 'mobile' ? 'default' : 'ghost'}
                  size="sm"
                  onClick={() => setViewMode('mobile')}
                  className="text-white"
                >
                  <Smartphone className="w-4 h-4 mr-1" />
                  Mobile
                </Button>
                <Button
                  variant={viewMode === 'desktop' ? 'default' : 'ghost'}
                  size="sm"
                  onClick={() => setViewMode('desktop')}
                  className="text-white"
                >
                  <Monitor className="w-4 h-4 mr-1" />
                  Desktop
                </Button>
              </div>
            )}

            {/* Performance Info Toggle */}
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setShowPerformanceInfo(!showPerformanceInfo)}
              className="text-white hover:bg-white/20"
            >
              <Info className="w-4 h-4" />
            </Button>

            {/* Controls Toggle */}
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setShowControls(!showControls)}
              className="text-white hover:bg-white/20"
            >
              <Settings className="w-4 h-4" />
            </Button>
          </div>
        </div>
      </div>

      {/* Performance Info Panel */}
      {showPerformanceInfo && (
        <Card className="fixed top-20 right-4 z-40 w-80 p-4 bg-black/40 backdrop-blur-md border-white/10">
          <h3 className="text-white font-semibold mb-3">
            🚀 {demoMode === 'tiktok' ? 'TikTok Feed' : 'Legacy Feed'} Features
          </h3>
          <div className="space-y-2 text-sm text-white/80">
            {demoMode === 'tiktok' ? (
              <>
                <div className="flex justify-between">
                  <span>Carousel-based:</span>
                  <Badge variant="secondary" className="bg-green-500/20 text-green-200">Embla</Badge>
                </div>
                <div className="flex justify-between">
                  <span>Snap Scrolling:</span>
                  <Badge variant="secondary" className="bg-green-500/20 text-green-200">Mandatory</Badge>
                </div>
                <div className="flex justify-between">
                  <span>Media Players:</span>
                  <Badge variant="secondary" className="bg-green-500/20 text-green-200">Specialized</Badge>
                </div>
                <div className="flex justify-between">
                  <span>Mood Theming:</span>
                  <Badge variant="secondary" className="bg-green-500/20 text-green-200">Dynamic</Badge>
                </div>
                <div className="flex justify-between">
                  <span>Accessibility:</span>
                  <Badge variant="secondary" className="bg-green-500/20 text-green-200">Enhanced</Badge>
                </div>
              </>
            ) : (
              <>
                <div className="flex justify-between">
                  <span>Content Preloading:</span>
                  <Badge variant="secondary" className="bg-green-500/20 text-green-200">Active</Badge>
                </div>
                <div className="flex justify-between">
                  <span>Memory Offloading:</span>
                  <Badge variant="secondary" className="bg-green-500/20 text-green-200">Active</Badge>
                </div>
                <div className="flex justify-between">
                  <span>Auto-play:</span>
                  <Badge variant="secondary" className="bg-green-500/20 text-green-200">Enabled</Badge>
                </div>
                <div className="flex justify-between">
                  <span>60fps Scrolling:</span>
                  <Badge variant="secondary" className="bg-green-500/20 text-green-200">Optimized</Badge>
                </div>
                <div className="flex justify-between">
                  <span>Virtualization:</span>
                  <Badge variant="secondary" className="bg-green-500/20 text-green-200">Enabled</Badge>
                </div>
              </>
            )}
          </div>
        </Card>
      )}

      {/* Main Content */}
      <div className="pt-20 h-screen">
        {demoMode === 'tiktok' ? (
          /* TikTok Feed Demo */
          <TikTokFeedDemo />
        ) : (
          /* Legacy Feed */
          <div className={cn(
            "h-full flex",
            viewMode === 'mobile' ? "flex-col" : "flex-row"
          )}>

            {/* Desktop Sidebar */}
            {viewMode === 'desktop' && (
              <div className="w-80 p-4 space-y-4 overflow-y-auto">
                <EnhancedFeedControls
                  feedType={feedType}
                  currentIndex={currentIndex}
                  totalItems={totalItems}
                  isPlaying={isPlaying}
                  onFeedTypeChange={handleFeedTypeChange}
                  onPrevious={handlePrevious}
                  onNext={handleNext}
                  onTogglePlay={handleTogglePlay}
                  onShuffle={handleShuffle}
                />
              </div>
            )}

            {/* Feed Container */}
            <div className="flex-1 relative">
              <LegacyVerticalFeedContainer
                feedType={feedType}
                filters={filters}
                onItemChange={(item, index) => {
                  handleItemChange(item, index)
                  setTotalItems(Math.max(totalItems, index + 1))
                }}
                onInteraction={handleInteraction}
                autoPlay={true}
                className="h-full"
              />

              {/* Bottom Navigation (Mobile) */}
              {viewMode === 'mobile' && (
                <EnhancedFeedNavigation
                  currentIndex={currentIndex}
                  totalItems={totalItems}
                  isPlaying={isPlaying}
                  onPrevious={handlePrevious}
                  onNext={handleNext}
                  onTogglePlay={handleTogglePlay}
                  onScrollToIndex={handleScrollToIndex}
                  showMoodFilter={true}
                  showPlayerControls={true}
                  className="fixed bottom-0 left-0 right-0"
                />
              )}
            </div>

            {/* Floating Controls Panel (Mobile) */}
            {viewMode === 'mobile' && showControls && (
              <div className="fixed top-20 left-4 z-40 w-80 max-h-[70vh] overflow-y-auto">
                <EnhancedFeedControls
                  feedType={feedType}
                  currentIndex={currentIndex}
                  totalItems={totalItems}
                  isPlaying={isPlaying}
                  onFeedTypeChange={handleFeedTypeChange}
                  onPrevious={handlePrevious}
                  onNext={handleNext}
                  onTogglePlay={handleTogglePlay}
                  onShuffle={handleShuffle}
                />
              </div>
            )}
          </div>
        )}
      </div>

      {/* Feature Showcase Overlay */}
      {demoMode === 'legacy' && (
        <div className="fixed bottom-4 left-4 z-50">
          <Card className="p-3 bg-black/40 backdrop-blur-md border-white/10">
            <div className="text-xs text-white/80 space-y-1">
              <div>📱 Swipe up/down to navigate</div>
              <div>🎵 Auto-play when in view</div>
              <div>⚡ Preloading 3 items ahead</div>
              <div>🧠 Memory management active</div>
            </div>
          </Card>
        </div>
      )}

      {/* Demo Mode Switch */}
      <div className="fixed bottom-4 right-4 z-50">
        <Card className="p-3 bg-black/40 backdrop-blur-md border-white/10">
          <div className="flex items-center gap-2">
            <Button
              onClick={() => setDemoMode(demoMode === 'tiktok' ? 'legacy' : 'tiktok')}
              variant="ghost"
              size="sm"
              className="text-white hover:bg-white/20"
            >
              {demoMode === 'tiktok' ? (
                <>
                  <ArrowLeft className="w-4 h-4 mr-1" />
                  Switch to Legacy
                </>
              ) : (
                <>
                  Switch to TikTok
                  <ArrowRight className="w-4 h-4 ml-1" />
                </>
              )}
            </Button>
          </div>
        </Card>
      </div>
    </div>
  )
}
