"use client"

import { TikTokFeedContainer } from '@/components/feed'
import { FeedType, InteractionData, FeedItem } from '@/types/feed'

export default function FollowingFeedPage() {
  const handleItemChange = (item: FeedItem, index: number) => {
    console.log('Following feed - Current item changed:', {
      title: item.post.title,
      index,
      creator: item.post.creator?.name || item.post.user.displayName,
      contentType: item.post.contentType
    })
  }

  const handleInteraction = (interaction: InteractionData) => {
    console.log('Following feed - User interaction:', interaction)

    // Add haptic feedback for mobile devices
    if ('vibrate' in navigator) {
      navigator.vibrate(50)
    }
  }

  return (
    <div className="h-screen bg-black overflow-hidden">
      <TikTokFeedContainer
        feedType={FeedType.FOLLOWING}
        onItemChange={handleItemChange}
        onInteraction={handleInteraction}
        autoPlay={true}
        showNavigation={true}
        showMoodSelector={false} // Following feed doesn't need mood selector
        enableHapticFeedback={true}
        preloadDistance={3}
        offloadDistance={10}
        className="w-full h-full"
      />
    </div>
  )
}
