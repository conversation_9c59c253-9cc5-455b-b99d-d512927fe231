import { useRef, useEffect, useCallback, useState } from 'react'

interface UseSwipeGesturesOptions {
  onSwipeUp?: () => void
  onSwipeDown?: () => void
  onSwipeLeft?: () => void
  onSwipeRight?: () => void
  threshold?: number // Minimum distance for a swipe
  velocityThreshold?: number // Minimum velocity for a swipe
  enableHapticFeedback?: boolean
}

interface TouchState {
  startX: number
  startY: number
  startTime: number
  currentX: number
  currentY: number
  isTracking: boolean
}

export function useSwipeGestures(options: UseSwipeGesturesOptions = {}) {
  const {
    onSwipeUp,
    onSwipeDown,
    onSwipeLeft,
    onSwipeRight,
    threshold = 50,
    velocityThreshold = 0.3,
    enableHapticFeedback = true,
  } = options

  const elementRef = useRef<HTMLElement>(null)
  const touchState = useRef<TouchState>({
    startX: 0,
    startY: 0,
    startTime: 0,
    currentX: 0,
    currentY: 0,
    isTracking: false,
  })

  const [isGesturing, setIsGesturing] = useState(false)

  // Haptic feedback function
  const triggerHapticFeedback = useCallback((type: 'light' | 'medium' | 'heavy' = 'light') => {
    if (!enableHapticFeedback || typeof window === 'undefined') return
    
    // Use the Vibration API if available
    if ('vibrate' in navigator) {
      const patterns = {
        light: [10],
        medium: [20],
        heavy: [30],
      }
      navigator.vibrate(patterns[type])
    }
  }, [enableHapticFeedback])

  // Handle touch start
  const handleTouchStart = useCallback((event: TouchEvent) => {
    const touch = event.touches[0]
    if (!touch) return

    touchState.current = {
      startX: touch.clientX,
      startY: touch.clientY,
      startTime: Date.now(),
      currentX: touch.clientX,
      currentY: touch.clientY,
      isTracking: true,
    }

    setIsGesturing(true)
  }, [])

  // Handle touch move
  const handleTouchMove = useCallback((event: TouchEvent) => {
    if (!touchState.current.isTracking) return

    const touch = event.touches[0]
    if (!touch) return

    touchState.current.currentX = touch.clientX
    touchState.current.currentY = touch.clientY

    // Prevent default scrolling for vertical swipes
    const deltaY = Math.abs(touch.clientY - touchState.current.startY)
    const deltaX = Math.abs(touch.clientX - touchState.current.startX)
    
    if (deltaY > deltaX && deltaY > threshold / 2) {
      event.preventDefault()
    }
  }, [threshold])

  // Handle touch end
  const handleTouchEnd = useCallback((event: TouchEvent) => {
    if (!touchState.current.isTracking) return

    const { startX, startY, startTime, currentX, currentY } = touchState.current
    const deltaX = currentX - startX
    const deltaY = currentY - startY
    const deltaTime = Date.now() - startTime
    const distance = Math.sqrt(deltaX * deltaX + deltaY * deltaY)
    const velocity = distance / deltaTime

    touchState.current.isTracking = false
    setIsGesturing(false)

    // Check if the swipe meets the threshold requirements
    if (distance < threshold || velocity < velocityThreshold) return

    // Determine swipe direction
    const absDeltaX = Math.abs(deltaX)
    const absDeltaY = Math.abs(deltaY)

    if (absDeltaY > absDeltaX) {
      // Vertical swipe
      if (deltaY < 0) {
        // Swipe up
        triggerHapticFeedback('light')
        onSwipeUp?.()
      } else {
        // Swipe down
        triggerHapticFeedback('light')
        onSwipeDown?.()
      }
    } else {
      // Horizontal swipe
      if (deltaX < 0) {
        // Swipe left
        triggerHapticFeedback('medium')
        onSwipeLeft?.()
      } else {
        // Swipe right
        triggerHapticFeedback('medium')
        onSwipeRight?.()
      }
    }
  }, [threshold, velocityThreshold, triggerHapticFeedback, onSwipeUp, onSwipeDown, onSwipeLeft, onSwipeRight])

  // Set up touch event listeners
  useEffect(() => {
    const element = elementRef.current
    if (!element) return

    // Add touch event listeners
    element.addEventListener('touchstart', handleTouchStart, { passive: false })
    element.addEventListener('touchmove', handleTouchMove, { passive: false })
    element.addEventListener('touchend', handleTouchEnd, { passive: true })

    return () => {
      element.removeEventListener('touchstart', handleTouchStart)
      element.removeEventListener('touchmove', handleTouchMove)
      element.removeEventListener('touchend', handleTouchEnd)
    }
  }, [handleTouchStart, handleTouchMove, handleTouchEnd])

  // Mouse event handlers for desktop testing
  const handleMouseDown = useCallback((event: MouseEvent) => {
    touchState.current = {
      startX: event.clientX,
      startY: event.clientY,
      startTime: Date.now(),
      currentX: event.clientX,
      currentY: event.clientY,
      isTracking: true,
    }
    setIsGesturing(true)
  }, [])

  const handleMouseMove = useCallback((event: MouseEvent) => {
    if (!touchState.current.isTracking) return

    touchState.current.currentX = event.clientX
    touchState.current.currentY = event.clientY
  }, [])

  const handleMouseUp = useCallback((event: MouseEvent) => {
    if (!touchState.current.isTracking) return

    const { startX, startY, startTime, currentX, currentY } = touchState.current
    const deltaX = currentX - startX
    const deltaY = currentY - startY
    const deltaTime = Date.now() - startTime
    const distance = Math.sqrt(deltaX * deltaX + deltaY * deltaY)
    const velocity = distance / deltaTime

    touchState.current.isTracking = false
    setIsGesturing(false)

    if (distance < threshold || velocity < velocityThreshold) return

    const absDeltaX = Math.abs(deltaX)
    const absDeltaY = Math.abs(deltaY)

    if (absDeltaY > absDeltaX) {
      if (deltaY < 0) {
        onSwipeUp?.()
      } else {
        onSwipeDown?.()
      }
    } else {
      if (deltaX < 0) {
        onSwipeLeft?.()
      } else {
        onSwipeRight?.()
      }
    }
  }, [threshold, velocityThreshold, onSwipeUp, onSwipeDown, onSwipeLeft, onSwipeRight])

  // Set up mouse event listeners for desktop
  useEffect(() => {
    const element = elementRef.current
    if (!element) return

    element.addEventListener('mousedown', handleMouseDown)
    element.addEventListener('mousemove', handleMouseMove)
    element.addEventListener('mouseup', handleMouseUp)

    return () => {
      element.removeEventListener('mousedown', handleMouseDown)
      element.removeEventListener('mousemove', handleMouseMove)
      element.removeEventListener('mouseup', handleMouseUp)
    }
  }, [handleMouseDown, handleMouseMove, handleMouseUp])

  return {
    elementRef,
    isGesturing,
    triggerHapticFeedback,
  }
}
