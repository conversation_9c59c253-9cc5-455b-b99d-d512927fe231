"use client"

import React, { useState } from 'react'
import { TikTokFeedContainer } from './TikTokFeedContainer'
import { FeedType, FeedItem, InteractionData } from '@/types/feed'
import { ContentType, ReactionType } from '@/types'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { 
  Play, 
  Pause, 
  RotateCcw, 
  Settings,
  Info
} from 'lucide-react'

// Mock data for demonstration
const mockFeedItems: FeedItem[] = [
  {
    id: '1',
    post: {
      id: '1',
      userId: 'user1',
      user: {
        id: 'user1',
        appwriteId: 'user1',
        email: '<EMAIL>',
        username: 'musiccreator',
        displayName: 'Music Creator',
        avatarUrl: 'https://images.unsplash.com/photo-1535713875002-d1d0cf377fde?w=100&h=100&fit=crop&crop=face',
        role: 'CREATOR' as any,
        isVerified: true,
        createdAt: new Date(),
        updatedAt: new Date()
      },
      creator: {
        id: 'creator1',
        userId: 'user1',
        user: {} as any,
        name: 'Music Creator',
        stageName: 'musiccreator',
        genre: ['Electronic', 'Ambient'],
        avatarUrl: 'https://images.unsplash.com/photo-1535713875002-d1d0cf377fde?w=100&h=100&fit=crop&crop=face',
        totalFollowers: 15420,
        totalViews: 1250000,
        totalLikes: 89500,
        createdAt: new Date(),
        updatedAt: new Date(),
        personas: []
      },
      title: 'Chill Vibes for Late Night Coding',
      description: 'Perfect ambient music to help you focus during those late night coding sessions. Let the smooth beats guide your creativity! 🎵✨ #coding #ambient #focus',
      content: 'Perfect ambient music to help you focus during those late night coding sessions.',
      contentType: ContentType.MUSIC,
      contentUrl: 'https://www.soundjay.com/misc/sounds/bell-ringing-05.wav',
      mediaUrls: [],
      thumbnailUrl: 'https://images.unsplash.com/photo-1493225457124-a3eb161ffa5f?w=400&h=400&fit=crop',
      moods: ['chill', 'peaceful'],
      viewCount: 45200,
      likeCount: 3420,
      shareCount: 892,
      isPublic: true,
      isExperimental: false,
      createdAt: new Date(),
      updatedAt: new Date(),
      reactions: [],
      memories: [],
      tags: ['ambient', 'coding', 'focus']
    },
    feedScore: 0.95,
    moodMatch: 0.88,
    insertedAt: new Date()
  },
  {
    id: '2',
    post: {
      id: '2',
      userId: 'user2',
      user: {
        id: 'user2',
        appwriteId: 'user2',
        email: '<EMAIL>',
        username: 'visualartist',
        displayName: 'Visual Artist',
        avatarUrl: 'https://images.unsplash.com/photo-1494790108755-2616b612b786?w=100&h=100&fit=crop&crop=face',
        role: 'CREATOR' as any,
        isVerified: true,
        createdAt: new Date(),
        updatedAt: new Date()
      },
      title: 'Digital Art Inspiration',
      description: 'Exploring the intersection of technology and creativity through digital art. What do you see in this piece? 🎨🤖 #digitalart #creativity #inspiration',
      content: 'Exploring the intersection of technology and creativity through digital art.',
      contentType: ContentType.IMAGE,
      contentUrl: 'https://images.unsplash.com/photo-1541961017774-22349e4a1262?w=800&h=1200&fit=crop',
      mediaUrls: [
        'https://images.unsplash.com/photo-1541961017774-22349e4a1262?w=800&h=1200&fit=crop',
        'https://images.unsplash.com/photo-1558618666-fcd25c85cd64?w=800&h=1200&fit=crop'
      ],
      thumbnailUrl: 'https://images.unsplash.com/photo-1541961017774-22349e4a1262?w=400&h=400&fit=crop',
      moods: ['inspired', 'energetic'],
      viewCount: 28900,
      likeCount: 2150,
      shareCount: 445,
      isPublic: true,
      isExperimental: false,
      createdAt: new Date(),
      updatedAt: new Date(),
      reactions: [],
      memories: [],
      tags: ['digitalart', 'creativity', 'inspiration']
    },
    feedScore: 0.87,
    moodMatch: 0.92,
    insertedAt: new Date()
  },
  {
    id: '3',
    post: {
      id: '3',
      userId: 'user3',
      user: {
        id: 'user3',
        appwriteId: 'user3',
        email: '<EMAIL>',
        username: 'storyteller',
        displayName: 'Story Teller',
        avatarUrl: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=100&h=100&fit=crop&crop=face',
        role: 'CREATOR' as any,
        isVerified: false,
        createdAt: new Date(),
        updatedAt: new Date()
      },
      title: 'The Art of Mindful Living',
      description: 'A reflection on finding peace in our chaotic world through mindfulness and intentional living.',
      content: `# The Art of Mindful Living

In our fast-paced world, finding moments of peace can feel like searching for a needle in a haystack. Yet, mindfulness offers us a path to tranquility that doesn't require escaping to a mountaintop monastery.

## What is Mindfulness?

Mindfulness is the practice of being fully present in the moment, aware of where we are and what we're doing, without being overly reactive to what's happening around us.

> "The present moment is the only time over which we have dominion." - Thích Nhất Hạnh

## Simple Practices for Daily Life

1. **Mindful Breathing**: Take three deep breaths before starting any new task
2. **Mindful Eating**: Savor each bite, noticing flavors and textures
3. **Mindful Walking**: Feel your feet connecting with the ground
4. **Mindful Listening**: Give your full attention to conversations

The beauty of mindfulness lies not in perfection, but in the gentle return to awareness whenever we notice our minds have wandered.

Remember: every moment is a new opportunity to begin again.`,
      contentType: ContentType.TEXT,
      contentUrl: '',
      mediaUrls: [],
      thumbnailUrl: 'https://images.unsplash.com/photo-1506905925346-21bda4d32df4?w=400&h=400&fit=crop',
      moods: ['peaceful', 'inspired'],
      viewCount: 12400,
      likeCount: 890,
      shareCount: 234,
      isPublic: true,
      isExperimental: false,
      createdAt: new Date(),
      updatedAt: new Date(),
      reactions: [],
      memories: [],
      tags: ['mindfulness', 'wellness', 'peace']
    },
    feedScore: 0.79,
    moodMatch: 0.85,
    insertedAt: new Date()
  },
  {
    id: '4',
    post: {
      id: '4',
      userId: 'user4',
      user: {
        id: 'user4',
        appwriteId: 'user4',
        email: '<EMAIL>',
        username: 'energydancer',
        displayName: 'Energy Dancer',
        avatarUrl: 'https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=100&h=100&fit=crop&crop=face',
        role: 'CREATOR' as any,
        isVerified: true,
        createdAt: new Date(),
        updatedAt: new Date()
      },
      title: 'High Energy Dance Routine',
      description: 'Get your body moving with this high-energy dance routine! Perfect for starting your day with positive vibes 💃⚡ #dance #energy #motivation',
      content: 'Get your body moving with this high-energy dance routine!',
      contentType: ContentType.VIDEO,
      contentUrl: 'https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/BigBuckBunny.mp4',
      mediaUrls: [],
      thumbnailUrl: 'https://images.unsplash.com/photo-1547036967-23d11aacaee0?w=400&h=400&fit=crop',
      moods: ['energetic', 'happy'],
      viewCount: 67800,
      likeCount: 5240,
      shareCount: 1120,
      isPublic: true,
      isExperimental: false,
      createdAt: new Date(),
      updatedAt: new Date(),
      reactions: [],
      memories: [],
      tags: ['dance', 'energy', 'motivation']
    },
    feedScore: 0.91,
    moodMatch: 0.94,
    insertedAt: new Date()
  }
]

export function TikTokFeedDemo() {
  const [currentFeedType, setCurrentFeedType] = useState<FeedType>(FeedType.DISCOVER)
  const [autoPlay, setAutoPlay] = useState(true)
  const [showDebugInfo, setShowDebugInfo] = useState(false)
  const [interactionLog, setInteractionLog] = useState<InteractionData[]>([])

  const handleItemChange = (item: FeedItem, index: number) => {
    console.log('Current item changed:', { item: item.post.title, index })
  }

  const handleInteraction = (interaction: InteractionData) => {
    console.log('User interaction:', interaction)
    setInteractionLog(prev => [interaction, ...prev.slice(0, 9)]) // Keep last 10 interactions
  }

  return (
    <div className="relative w-full h-screen bg-black">
      {/* Main TikTok Feed */}
      <TikTokFeedContainer
        feedType={currentFeedType}
        onItemChange={handleItemChange}
        onInteraction={handleInteraction}
        autoPlay={autoPlay}
        showNavigation={true}
        showMoodSelector={true}
        enableHapticFeedback={true}
        className="w-full h-full"
      />

      {/* Demo Controls */}
      <div className="absolute top-4 left-4 z-40 space-y-2">
        <Badge variant="secondary" className="bg-black/70 text-white border-white/20">
          TikTok Feed Demo
        </Badge>
        
        <div className="flex space-x-2">
          <Button
            onClick={() => setAutoPlay(!autoPlay)}
            variant="ghost"
            size="sm"
            className="bg-black/70 text-white hover:bg-black/80"
          >
            {autoPlay ? <Pause className="h-4 w-4" /> : <Play className="h-4 w-4" />}
          </Button>
          
          <Button
            onClick={() => setShowDebugInfo(!showDebugInfo)}
            variant="ghost"
            size="sm"
            className="bg-black/70 text-white hover:bg-black/80"
          >
            <Info className="h-4 w-4" />
          </Button>
          
          <Button
            onClick={() => window.location.reload()}
            variant="ghost"
            size="sm"
            className="bg-black/70 text-white hover:bg-black/80"
          >
            <RotateCcw className="h-4 w-4" />
          </Button>
        </div>
      </div>

      {/* Debug Information */}
      {showDebugInfo && (
        <div className="absolute top-4 right-4 z-40 bg-black/80 backdrop-blur-sm text-white p-4 rounded-lg max-w-sm">
          <div className="flex items-center justify-between mb-3">
            <h3 className="font-semibold text-hvppy-400">Debug Info</h3>
            <Button
              onClick={() => setShowDebugInfo(false)}
              variant="ghost"
              size="sm"
              className="h-6 w-6 p-0 text-white/60 hover:text-white"
            >
              ×
            </Button>
          </div>
          
          <div className="space-y-2 text-xs">
            <div>
              <span className="text-white/60">Feed Type:</span>
              <span className="ml-2">{currentFeedType}</span>
            </div>
            <div>
              <span className="text-white/60">Auto Play:</span>
              <span className="ml-2">{autoPlay ? 'On' : 'Off'}</span>
            </div>
            <div>
              <span className="text-white/60">Total Items:</span>
              <span className="ml-2">{mockFeedItems.length}</span>
            </div>
          </div>

          {/* Recent Interactions */}
          {interactionLog.length > 0 && (
            <div className="mt-4">
              <h4 className="font-medium text-hvppy-400 mb-2">Recent Interactions</h4>
              <div className="space-y-1 max-h-32 overflow-y-auto">
                {interactionLog.map((interaction, index) => (
                  <div key={index} className="text-xs text-white/70">
                    <span className="text-hvppy-300">{interaction.type}</span>
                    <span className="ml-2">Post {interaction.postId}</span>
                  </div>
                ))}
              </div>
            </div>
          )}
        </div>
      )}

      {/* Instructions */}
      <div className="absolute bottom-4 left-1/2 transform -translate-x-1/2 z-40 text-center">
        <div className="bg-black/70 backdrop-blur-sm text-white px-4 py-2 rounded-lg text-sm">
          <p className="mb-1">🎉 <strong>TikTok-Inspired Vertical Feed Demo</strong></p>
          <p className="text-white/70">
            Swipe up/down • Tap for controls • Double tap to like
          </p>
        </div>
      </div>
    </div>
  )
}

// Export mock data for testing
export { mockFeedItems }
