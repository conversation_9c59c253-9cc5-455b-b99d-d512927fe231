"use client"

import { TikTokFeedContainer } from '@/components/feed'
import { FeedType, InteractionData, FeedItem } from '@/types/feed'

export default function MoodFeedPage() {
  const handleItemChange = (item: FeedItem, index: number) => {
    console.log('Mood feed - Current item changed:', {
      title: item.post.title,
      index,
      moods: item.post.moods,
      moodMatch: item.moodMatch,
      contentType: item.post.contentType
    })
  }

  const handleInteraction = (interaction: InteractionData) => {
    console.log('Mood feed - User interaction:', interaction)

    // Add haptic feedback for mobile devices
    if ('vibrate' in navigator) {
      navigator.vibrate(50)
    }
  }

  return (
    <div className="h-screen bg-black overflow-hidden">
      <TikTokFeedContainer
        feedType={FeedType.MOOD_BASED}
        onItemChange={handleItemChange}
        onInteraction={handleInteraction}
        autoPlay={true}
        showNavigation={true}
        showMoodSelector={true} // Mood feed should definitely show mood selector
        enableHapticFeedback={true}
        preloadDistance={3}
        offloadDistance={10}
        className="w-full h-full"
      />
    </div>
  )
}
